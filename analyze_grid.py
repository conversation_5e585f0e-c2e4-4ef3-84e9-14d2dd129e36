#!/usr/bin/env python3

# Sample grids from the output
grids = [
    # Grid 1
    [[112, 117, 110, 91, 126],
     [123, 99, 104, 93, 81],
     [77, 62, 85, 74, 95],
     [60, 102, 111, 115, 76],
     [49, 48, 91, 35, 44]],
    
    # <PERSON>rid 2
    [[53, 98, 58, 35, 97],
     [37, 97, 90, 108, 96],
     [69, 111, 62, 97, 84],
     [113, 96, 52, 39, 99],
     [96, 79, 117, 100, 74]],
    
    # <PERSON><PERSON> 3
    [[55, 65, 99, 63, 101],
     [97, 73, 103, 42, 97],
     [70, 98, 71, 124, 95],
     [79, 72, 110, 36, 95],
     [37, 126, 113, 37, 85]],
    
    # Grid 4
    [[75, 63, 71, 116, 69],
     [44, 76, 88, 45, 37],
     [123, 119, 121, 70, 96],
     [76, 34, 99, 52, 84],
     [59, 55, 78, 58, 60]],
    
    # <PERSON>rid 5
    [[54, 76, 66, 63, 102],
     [116, 67, 115, 82, 76],
     [62, 123, 117, 55, 105],
     [115, 74, 90, 110, 49],
     [40, 109, 105, 98, 91]],
    
    # <PERSON>rid 6
    [[124, 66, 81, 79, 56],
     [123, 51, 95, 58, 36],
     [56, 51, 66, 98, 120],
     [117, 59, 34, 69, 81],
     [85, 67, 39, 52, 101]],
    
    # Grid 7
    [[43, 52, 120, 36, 56],
     [33, 51, 70, 98, 32],
     [69, 78, 62, 79, 108],
     [77, 116, 43, 48, 87],
     [120, 64, 111, 102, 96]],
    
    # Grid 8 (same as Grid 1 - pattern repeats?)
    [[42, 74, 34, 83, 67],
     [62, 51, 120, 53, 103],
     [41, 107, 70, 32, 40],
     [105, 74, 114, 84, 92],
     [73, 79, 66, 122, 119]]
]

def grid_to_ascii(grid):
    """Convert a grid of numbers to ASCII characters"""
    result = []
    for row in grid:
        ascii_row = ''.join(chr(num) for num in row)
        result.append(ascii_row)
    return result

def print_grid_analysis(grid_num, grid):
    print(f"\n=== Grid {grid_num} ===")
    print("Numbers:")
    for row in grid:
        print(' '.join(f'{num:3d}' for num in row))
    
    print("\nASCII:")
    ascii_grid = grid_to_ascii(grid)
    for row in ascii_grid:
        print(repr(row))
    
    print("\nReadable:")
    for row in ascii_grid:
        print(row)

# Analyze all grids
for i, grid in enumerate(grids, 1):
    print_grid_analysis(i, grid)

# Look for patterns
print("\n=== PATTERN ANALYSIS ===")
print("Looking for readable text in different orientations...")

# Try reading each grid in different ways
for i, grid in enumerate(grids, 1):
    ascii_grid = grid_to_ascii(grid)

    print(f"\nGrid {i} - Row by row:")
    for row in ascii_grid:
        print(f"  {repr(row)}")

    print(f"Grid {i} - Column by column:")
    for col in range(5):
        column = ''.join(ascii_grid[row][col] for row in range(5))
        print(f"  Col {col}: {repr(column)}")

    print(f"Grid {i} - Diagonal (top-left to bottom-right):")
    diagonal1 = ''.join(ascii_grid[i][i] for i in range(5))
    print(f"  {repr(diagonal1)}")

    print(f"Grid {i} - Diagonal (top-right to bottom-left):")
    diagonal2 = ''.join(ascii_grid[i][4-i] for i in range(5))
    print(f"  {repr(diagonal2)}")

# Extract patterns from columns
print("\n=== COLUMN PATTERN ANALYSIS ===")
for col in range(5):
    print(f"\nColumn {col} across all grids:")
    column_sequence = ""
    for i, grid in enumerate(grids, 1):
        ascii_grid = grid_to_ascii(grid)
        column = ''.join(ascii_grid[row][col] for row in range(5))
        column_sequence += column
        print(f"  Grid {i}: {repr(column)}")
    print(f"  Combined: {repr(column_sequence)}")

    # Try reading just the first character of each column
    first_chars = ""
    for grid in grids:
        ascii_grid = grid_to_ascii(grid)
        first_chars += ascii_grid[0][col]
    print(f"  First chars: {repr(first_chars)}")

# Try reading the first character of each grid
print("\n=== FIRST CHARACTER ANALYSIS ===")
first_chars_all = ""
for i, grid in enumerate(grids, 1):
    ascii_grid = grid_to_ascii(grid)
    first_char = ascii_grid[0][0]
    first_chars_all += first_char
    print(f"Grid {i} first char: {repr(first_char)}")
print(f"All first chars: {repr(first_chars_all)}")

# Try reading diagonals across grids
print("\n=== DIAGONAL ANALYSIS ===")
diagonal_sequence = ""
for grid in grids:
    ascii_grid = grid_to_ascii(grid)
    diagonal = ''.join(ascii_grid[i][i] for i in range(5))
    diagonal_sequence += diagonal
print(f"All diagonals combined: {repr(diagonal_sequence)}")
